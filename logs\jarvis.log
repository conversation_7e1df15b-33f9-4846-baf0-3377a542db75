2025-05-29 20:47:05,658 - jarvis.memory - INFO - memory:110 - Created new memory collection
2025-05-29 20:48:05,308 - jarvis.memory - INFO - memory:117 - Memory manager initialized successfully
2025-05-29 20:48:06,265 - jarvis.llm - INFO - llm:50 - Initialized Groq provider with model: mixtral-8x7b-32768
2025-05-29 20:48:06,266 - jarvis.llm - INFO - llm:171 - Groq provider initialized
2025-05-29 20:48:06,486 - jarvis.llm - INFO - llm:105 - Initialized OpenAI provider with model: gpt-3.5-turbo
2025-05-29 20:48:06,487 - jarvis.llm - INFO - llm:180 - OpenAI provider initialized
2025-05-29 20:48:06,488 - jarvis.llm - INFO - llm:185 - Set current provider to: groq
2025-05-29 20:49:56,648 - jarvis.memory - INFO - memory:104 - Loaded existing memory collection
2025-05-29 20:50:02,358 - jarvis.memory - INFO - memory:117 - Memory manager initialized successfully
2025-05-29 20:50:02,798 - jarvis.llm - INFO - llm:50 - Initialized Groq provider with model: mixtral-8x7b-32768
2025-05-29 20:50:02,800 - jarvis.llm - INFO - llm:171 - Groq provider initialized
2025-05-29 20:50:02,991 - jarvis.llm - INFO - llm:105 - Initialized OpenAI provider with model: gpt-3.5-turbo
2025-05-29 20:50:02,993 - jarvis.llm - INFO - llm:180 - OpenAI provider initialized
2025-05-29 20:50:02,994 - jarvis.llm - INFO - llm:185 - Set current provider to: groq
2025-05-29 20:50:52,313 - jarvis.memory - INFO - memory:104 - Loaded existing memory collection
2025-05-29 20:50:57,881 - jarvis.memory - INFO - memory:117 - Memory manager initialized successfully
2025-05-29 20:50:58,298 - jarvis.llm - INFO - llm:50 - Initialized Groq provider with model: mixtral-8x7b-32768
2025-05-29 20:50:58,300 - jarvis.llm - INFO - llm:171 - Groq provider initialized
2025-05-29 20:50:58,484 - jarvis.llm - INFO - llm:105 - Initialized OpenAI provider with model: gpt-3.5-turbo
2025-05-29 20:50:58,486 - jarvis.llm - INFO - llm:180 - OpenAI provider initialized
2025-05-29 20:50:58,488 - jarvis.llm - INFO - llm:185 - Set current provider to: groq
2025-05-29 20:51:31,103 - jarvis.memory - INFO - memory:104 - Loaded existing memory collection
2025-05-29 20:51:35,093 - jarvis.memory - INFO - memory:117 - Memory manager initialized successfully
2025-05-29 20:51:35,472 - jarvis.llm - INFO - llm:50 - Initialized Groq provider with model: mixtral-8x7b-32768
2025-05-29 20:51:35,473 - jarvis.llm - INFO - llm:171 - Groq provider initialized
2025-05-29 20:51:35,653 - jarvis.llm - INFO - llm:105 - Initialized OpenAI provider with model: gpt-3.5-turbo
2025-05-29 20:51:35,655 - jarvis.llm - INFO - llm:180 - OpenAI provider initialized
2025-05-29 20:51:35,656 - jarvis.llm - INFO - llm:185 - Set current provider to: groq
2025-05-29 20:51:35,783 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: planner
2025-05-29 20:51:35,784 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: developer
2025-05-29 20:51:35,785 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: shell
2025-05-29 20:51:35,786 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: file_manager
2025-05-29 20:51:35,787 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: browser
2025-05-29 20:51:35,788 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: system_control
2025-05-29 20:51:35,789 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: research
2025-05-29 20:51:35,790 - jarvis.dispatcher - INFO - agent_dispatcher:55 - Initialized 7 agents
2025-05-29 20:53:40,248 - jarvis.memory - INFO - memory:104 - Loaded existing memory collection
2025-05-29 20:53:45,027 - jarvis.memory - INFO - memory:117 - Memory manager initialized successfully
2025-05-29 20:53:45,417 - jarvis.llm - INFO - llm:50 - Initialized Groq provider with model: mixtral-8x7b-32768
2025-05-29 20:53:45,418 - jarvis.llm - INFO - llm:171 - Groq provider initialized
2025-05-29 20:53:45,598 - jarvis.llm - INFO - llm:105 - Initialized OpenAI provider with model: gpt-3.5-turbo
2025-05-29 20:53:45,599 - jarvis.llm - INFO - llm:180 - OpenAI provider initialized
2025-05-29 20:53:45,600 - jarvis.llm - INFO - llm:185 - Set current provider to: groq
2025-05-29 20:53:45,695 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: planner
2025-05-29 20:53:45,696 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: developer
2025-05-29 20:53:45,697 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: shell
2025-05-29 20:53:45,698 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: file_manager
2025-05-29 20:53:45,699 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: browser
2025-05-29 20:53:45,699 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: system_control
2025-05-29 20:53:45,700 - jarvis.dispatcher - INFO - agent_dispatcher:51 - Initialized agent: research
2025-05-29 20:53:45,701 - jarvis.dispatcher - INFO - agent_dispatcher:55 - Initialized 7 agents
