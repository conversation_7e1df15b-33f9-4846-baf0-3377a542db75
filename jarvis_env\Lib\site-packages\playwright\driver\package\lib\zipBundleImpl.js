"use strict";var pt=Object.create;var se=Object.defineProperty;var mt=Object.getOwnPropertyDescriptor;var xt=Object.getOwnPropertyNames;var vt=Object.getPrototypeOf,Et=Object.prototype.hasOwnProperty;var E=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),wt=(e,r)=>{for(var t in r)se(e,t,{get:r[t],enumerable:!0})},or=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of xt(r))!Et.call(e,i)&&i!==t&&se(e,i,{get:()=>r[i],enumerable:!(n=mt(r,i))||n.enumerable});return e};var Te=(e,r,t)=>(t=e!=null?pt(vt(e)):{},or(r||!e||!e.__esModule?se(t,"default",{value:e,enumerable:!0}):t,e)),gt=e=>or(se({},"__esModule",{value:!0}),e);var Be=E((Dn,sr)=>{var U=require("buffer").Buffer,Ue=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];typeof Int32Array!="undefined"&&(Ue=new Int32Array(Ue));function fr(e){if(U.isBuffer(e))return e;var r=typeof U.alloc=="function"&&typeof U.from=="function";if(typeof e=="number")return r?U.alloc(e):new U(e);if(typeof e=="string")return r?U.from(e):new U(e);throw new Error("input must be buffer, number, or string, received "+typeof e)}function yt(e){var r=fr(4);return r.writeInt32BE(e,0),r}function Ne(e,r){e=fr(e),U.isBuffer(r)&&(r=r.readUInt32BE(0));for(var t=~~r^-1,n=0;n<e.length;n++)t=Ue[(t^e[n])&255]^t>>>8;return t^-1}function Me(){return yt(Ne.apply(null,arguments))}Me.signed=function(){return Ne.apply(null,arguments)};Me.unsigned=function(){return Ne.apply(null,arguments)>>>0};sr.exports=Me});var br=E(Ge=>{var ar=require("fs"),de=require("stream").Transform,ur=require("stream").PassThrough,cr=require("zlib"),We=require("util"),Ct=require("events").EventEmitter,lr=Be();Ge.ZipFile=W;Ge.dateToDosDateTime=Cr;We.inherits(W,Ct);function W(){this.outputStream=new ur,this.entries=[],this.outputStreamCursor=0,this.ended=!1,this.allDone=!1,this.forceZip64Eocd=!1}W.prototype.addFile=function(e,r,t){var n=this;r=he(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),ar.stat(e,function(o,f){if(o)return n.emit("error",o);if(!f.isFile())return n.emit("error",new Error("not a file: "+e));i.uncompressedSize=f.size,t.mtime==null&&i.setLastModDate(f.mtime),t.mode==null&&i.setFileAttributesMode(f.mode),i.setFileDataPumpFunction(function(){var s=ar.createReadStream(e);i.state=m.FILE_DATA_IN_PROGRESS,s.on("error",function(u){n.emit("error",u)}),dr(n,i,s)}),M(n)})};W.prototype.addReadStream=function(e,r,t){var n=this;r=he(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),i.setFileDataPumpFunction(function(){i.state=m.FILE_DATA_IN_PROGRESS,dr(n,i,e)}),M(n)};W.prototype.addBuffer=function(e,r,t){var n=this;if(r=he(r,!1),e.length>1073741823)throw new Error("buffer too large: "+e.length+" > 1073741823");if(t==null&&(t={}),t.size!=null)throw new Error("options.size not allowed");var i=new m(r,!1,t);i.uncompressedSize=e.length,i.crc32=lr.unsigned(e),i.crcAndFileSizeKnown=!0,n.entries.push(i),i.compress?cr.deflateRaw(e,function(f,s){o(s)}):o(e);function o(f){i.compressedSize=f.length,i.setFileDataPumpFunction(function(){Z(n,f),Z(n,i.getDataDescriptor()),i.state=m.FILE_DATA_DONE,setImmediate(function(){M(n)})}),M(n)}};W.prototype.addEmptyDirectory=function(e,r){var t=this;if(e=he(e,!0),r==null&&(r={}),r.size!=null)throw new Error("options.size not allowed");if(r.compress!=null)throw new Error("options.compress not allowed");var n=new m(e,!0,r);t.entries.push(n),n.setFileDataPumpFunction(function(){Z(t,n.getDataDescriptor()),n.state=m.FILE_DATA_DONE,M(t)}),M(t)};var bt=N([80,75,5,6]);W.prototype.end=function(e,r){if(typeof e=="function"&&(r=e,e=null),e==null&&(e={}),!this.ended){if(this.ended=!0,this.finalSizeCallback=r,this.forceZip64Eocd=!!e.forceZip64Format,e.comment){if(typeof e.comment=="string"?this.comment=It(e.comment):this.comment=e.comment,this.comment.length>65535)throw new Error("comment is too large");if(re(this.comment,bt))throw new Error("comment contains end of central directory record signature")}else this.comment=pe;M(this)}};function Z(e,r){e.outputStream.write(r),e.outputStreamCursor+=r.length}function dr(e,r,t){var n=new He,i=new le,o=r.compress?new cr.DeflateRaw:new ur,f=new le;t.pipe(n).pipe(i).pipe(o).pipe(f).pipe(e.outputStream,{end:!1}),f.on("end",function(){if(r.crc32=n.crc32,r.uncompressedSize==null)r.uncompressedSize=i.byteCount;else if(r.uncompressedSize!==i.byteCount)return e.emit("error",new Error("file data stream has unexpected number of bytes"));r.compressedSize=f.byteCount,e.outputStreamCursor+=r.compressedSize,Z(e,r.getDataDescriptor()),r.state=m.FILE_DATA_DONE,M(e)})}function M(e){if(e.allDone)return;if(e.ended&&e.finalSizeCallback!=null){var r=Ft(e);r!=null&&(e.finalSizeCallback(r),e.finalSizeCallback=null)}var t=n();function n(){for(var o=0;o<e.entries.length;o++){var f=e.entries[o];if(f.state<m.FILE_DATA_DONE)return f}return null}if(t!=null){if(t.state<m.READY_TO_PUMP_FILE_DATA||t.state===m.FILE_DATA_IN_PROGRESS)return;t.relativeOffsetOfLocalHeader=e.outputStreamCursor;var i=t.getLocalFileHeader();Z(e,i),t.doFileDataPump()}else e.ended&&(e.offsetOfStartOfCentralDirectory=e.outputStreamCursor,e.entries.forEach(function(o){var f=o.getCentralDirectoryRecord();Z(e,f)}),Z(e,St(e)),e.outputStream.end(),e.allDone=!0)}function Ft(e){for(var r=0,t=0,n=0;n<e.entries.length;n++){var i=e.entries[n];if(i.compress)return-1;if(i.state>=m.READY_TO_PUMP_FILE_DATA){if(i.uncompressedSize==null)return-1}else if(i.uncompressedSize==null)return null;i.relativeOffsetOfLocalHeader=r;var o=i.useZip64Format();r+=hr+i.utf8FileName.length,r+=i.uncompressedSize,i.crcAndFileSizeKnown||(o?r+=gr:r+=wr),t+=yr+i.utf8FileName.length+i.fileComment.length,o&&(t+=Pe)}var f=0;return(e.forceZip64Eocd||e.entries.length>=65535||t>=65535||r>=4294967295)&&(f+=ue+qe),f+=ce+e.comment.length,r+t+f}var ue=56,qe=20,ce=22;function St(e,r){var t=!1,n=e.entries.length;(e.forceZip64Eocd||e.entries.length>=65535)&&(n=65535,t=!0);var i=e.outputStreamCursor-e.offsetOfStartOfCentralDirectory,o=i;(e.forceZip64Eocd||i>=4294967295)&&(o=4294967295,t=!0);var f=e.offsetOfStartOfCentralDirectory;if((e.forceZip64Eocd||e.offsetOfStartOfCentralDirectory>=4294967295)&&(f=4294967295,t=!0),r)return t?ue+qe+ce:ce;var s=F(ce+e.comment.length);if(s.writeUInt32LE(101010256,0),s.writeUInt16LE(0,4),s.writeUInt16LE(0,6),s.writeUInt16LE(n,8),s.writeUInt16LE(n,10),s.writeUInt32LE(o,12),s.writeUInt32LE(f,16),s.writeUInt16LE(e.comment.length,20),e.comment.copy(s,22),!t)return s;var u=F(ue);u.writeUInt32LE(101075792,0),L(u,ue-12,4),u.writeUInt16LE(xr,12),u.writeUInt16LE(mr,14),u.writeUInt32LE(0,16),u.writeUInt32LE(0,20),L(u,e.entries.length,24),L(u,e.entries.length,32),L(u,i,40),L(u,e.offsetOfStartOfCentralDirectory,48);var l=F(qe);return l.writeUInt32LE(117853008,0),l.writeUInt32LE(0,4),L(l,e.outputStreamCursor,8),l.writeUInt32LE(1,16),Buffer.concat([u,l,s])}function he(e,r){if(e==="")throw new Error("empty metadataPath");if(e=e.replace(/\\/g,"/"),/^[a-zA-Z]:/.test(e)||/^\//.test(e))throw new Error("absolute path: "+e);if(e.split("/").indexOf("..")!==-1)throw new Error("invalid relative path: "+e);var t=/\/$/.test(e);if(r)t||(e+="/");else if(t)throw new Error("file path cannot end with '/': "+e);return e}var pe=F(0);function m(e,r,t){if(this.utf8FileName=N(e),this.utf8FileName.length>65535)throw new Error("utf8 file name too long. "+utf8FileName.length+" > 65535");if(this.isDirectory=r,this.state=m.WAITING_FOR_METADATA,this.setLastModDate(t.mtime!=null?t.mtime:new Date),t.mode!=null?this.setFileAttributesMode(t.mode):this.setFileAttributesMode(r?16893:33204),r?(this.crcAndFileSizeKnown=!0,this.crc32=0,this.uncompressedSize=0,this.compressedSize=0):(this.crcAndFileSizeKnown=!1,this.crc32=null,this.uncompressedSize=null,this.compressedSize=null,t.size!=null&&(this.uncompressedSize=t.size)),r?this.compress=!1:(this.compress=!0,t.compress!=null&&(this.compress=!!t.compress)),this.forceZip64Format=!!t.forceZip64Format,t.fileComment){if(typeof t.fileComment=="string"?this.fileComment=N(t.fileComment,"utf-8"):this.fileComment=t.fileComment,this.fileComment.length>65535)throw new Error("fileComment is too large")}else this.fileComment=pe}m.WAITING_FOR_METADATA=0;m.READY_TO_PUMP_FILE_DATA=1;m.FILE_DATA_IN_PROGRESS=2;m.FILE_DATA_DONE=3;m.prototype.setLastModDate=function(e){var r=Cr(e);this.lastModFileTime=r.time,this.lastModFileDate=r.date};m.prototype.setFileAttributesMode=function(e){if((e&65535)!==e)throw new Error("invalid mode. expected: 0 <= "+e+" <= 65535");this.externalFileAttributes=e<<16>>>0};m.prototype.setFileDataPumpFunction=function(e){this.doFileDataPump=e,this.state=m.READY_TO_PUMP_FILE_DATA};m.prototype.useZip64Format=function(){return this.forceZip64Format||this.uncompressedSize!=null&&this.uncompressedSize>4294967294||this.compressedSize!=null&&this.compressedSize>4294967294||this.relativeOffsetOfLocalHeader!=null&&this.relativeOffsetOfLocalHeader>4294967294};var hr=30,pr=20,mr=45,xr=831,vr=2048,Er=8;m.prototype.getLocalFileHeader=function(){var e=0,r=0,t=0;this.crcAndFileSizeKnown&&(e=this.crc32,r=this.compressedSize,t=this.uncompressedSize);var n=F(hr),i=vr;return this.crcAndFileSizeKnown||(i|=Er),n.writeUInt32LE(67324752,0),n.writeUInt16LE(pr,4),n.writeUInt16LE(i,6),n.writeUInt16LE(this.getCompressionMethod(),8),n.writeUInt16LE(this.lastModFileTime,10),n.writeUInt16LE(this.lastModFileDate,12),n.writeUInt32LE(e,14),n.writeUInt32LE(r,18),n.writeUInt32LE(t,22),n.writeUInt16LE(this.utf8FileName.length,26),n.writeUInt16LE(0,28),Buffer.concat([n,this.utf8FileName])};var wr=16,gr=24;m.prototype.getDataDescriptor=function(){if(this.crcAndFileSizeKnown)return pe;if(this.useZip64Format()){var e=F(gr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),L(e,this.compressedSize,8),L(e,this.uncompressedSize,16),e}else{var e=F(wr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),e.writeUInt32LE(this.compressedSize,8),e.writeUInt32LE(this.uncompressedSize,12),e}};var yr=46,Pe=28;m.prototype.getCentralDirectoryRecord=function(){var e=F(yr),r=vr;this.crcAndFileSizeKnown||(r|=Er);var t=this.compressedSize,n=this.uncompressedSize,i=this.relativeOffsetOfLocalHeader,o,f;return this.useZip64Format()?(t=4294967295,n=4294967295,i=4294967295,o=mr,f=F(Pe),f.writeUInt16LE(1,0),f.writeUInt16LE(Pe-4,2),L(f,this.uncompressedSize,4),L(f,this.compressedSize,12),L(f,this.relativeOffsetOfLocalHeader,20)):(o=pr,f=pe),e.writeUInt32LE(33639248,0),e.writeUInt16LE(xr,4),e.writeUInt16LE(o,6),e.writeUInt16LE(r,8),e.writeUInt16LE(this.getCompressionMethod(),10),e.writeUInt16LE(this.lastModFileTime,12),e.writeUInt16LE(this.lastModFileDate,14),e.writeUInt32LE(this.crc32,16),e.writeUInt32LE(t,20),e.writeUInt32LE(n,24),e.writeUInt16LE(this.utf8FileName.length,28),e.writeUInt16LE(f.length,30),e.writeUInt16LE(this.fileComment.length,32),e.writeUInt16LE(0,34),e.writeUInt16LE(0,36),e.writeUInt32LE(this.externalFileAttributes,38),e.writeUInt32LE(i,42),Buffer.concat([e,this.utf8FileName,f,this.fileComment])};m.prototype.getCompressionMethod=function(){var e=0,r=8;return this.compress?r:e};function Cr(e){var r=0;r|=e.getDate()&31,r|=(e.getMonth()+1&15)<<5,r|=(e.getFullYear()-1980&127)<<9;var t=0;return t|=Math.floor(e.getSeconds()/2),t|=(e.getMinutes()&63)<<5,t|=(e.getHours()&31)<<11,{date:r,time:t}}function L(e,r,t){var n=Math.floor(r/4294967296),i=r%4294967296;e.writeUInt32LE(i,t),e.writeUInt32LE(n,t+4)}We.inherits(le,de);function le(e){de.call(this,e),this.byteCount=0}le.prototype._transform=function(e,r,t){this.byteCount+=e.length,t(null,e)};We.inherits(He,de);function He(e){de.call(this,e),this.crc32=0}He.prototype._transform=function(e,r,t){this.crc32=lr.unsigned(e,this.crc32),t(null,e)};var Ze="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";if(Ze.length!==256)throw new Error("assertion failure");var ae=null;function It(e){if(/^[\x20-\x7e]*$/.test(e))return N(e,"utf-8");if(ae==null){ae={};for(var r=0;r<Ze.length;r++)ae[Ze[r]]=r}for(var t=F(e.length),r=0;r<e.length;r++){var n=ae[e[r]];if(n==null)throw new Error("character not encodable in CP437: "+JSON.stringify(e[r]));t[r]=n}return t}function F(e){F=r;try{return F(e)}catch{return F=t,F(e)}function r(n){return Buffer.allocUnsafe(n)}function t(n){return new Buffer(n)}}function N(e,r){N=t;try{return N(e,r)}catch{return N=n,N(e,r)}function t(i,o){return Buffer.from(i,o)}function n(i,o){return new Buffer(i,o)}}function re(e,r){re=t;try{return re(e,r)}catch{return re=n,re(e,r)}function t(i,o){return i.includes(o)}function n(i,o){for(var f=0;f<=i.length-o.length;f++)for(var s=0;;s++){if(s===o.length)return!0;if(i[f+s]!==o[s])break}return!1}}});var Lr=E((Un,Ir)=>{Ir.exports=me;function me(){this.pending=0,this.max=1/0,this.listeners=[],this.waiting=[],this.error=null}me.prototype.go=function(e){this.pending<this.max?Sr(this,e):this.waiting.push(e)};me.prototype.wait=function(e){this.pending===0?e(this.error):this.listeners.push(e)};me.prototype.hold=function(){return Fr(this)};function Fr(e){e.pending+=1;var r=!1;return t;function t(i){if(r)throw new Error("callback called twice");if(r=!0,e.error=e.error||i,e.pending-=1,e.waiting.length>0&&e.pending<e.max)Sr(e,e.waiting.shift());else if(e.pending===0){var o=e.listeners;e.listeners=[],o.forEach(n)}}function n(i){i(e.error)}}function Sr(e,r){r(Fr(e))}});var zr=E(ne=>{var te=require("fs"),xe=require("util"),Ye=require("stream"),Or=Ye.Readable,$e=Ye.Writable,Lt=Ye.PassThrough,Ot=Lr(),ve=require("events").EventEmitter;ne.createFromBuffer=zt;ne.createFromFd=_t;ne.BufferSlicer=D;ne.FdSlicer=R;xe.inherits(R,ve);function R(e,r){r=r||{},ve.call(this),this.fd=e,this.pend=new Ot,this.pend.max=1,this.refCount=0,this.autoClose=!!r.autoClose}R.prototype.read=function(e,r,t,n,i){var o=this;o.pend.go(function(f){te.read(o.fd,e,r,t,n,function(s,u,l){f(),i(s,u,l)})})};R.prototype.write=function(e,r,t,n,i){var o=this;o.pend.go(function(f){te.write(o.fd,e,r,t,n,function(s,u,l){f(),i(s,u,l)})})};R.prototype.createReadStream=function(e){return new Ee(this,e)};R.prototype.createWriteStream=function(e){return new we(this,e)};R.prototype.ref=function(){this.refCount+=1};R.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.autoClose&&te.close(e.fd,r);function r(t){t?e.emit("error",t):e.emit("close")}};xe.inherits(Ee,Or);function Ee(e,r){r=r||{},Or.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end,this.pos=this.start,this.destroyed=!1}Ee.prototype._read=function(e){var r=this;if(!r.destroyed){var t=Math.min(r._readableState.highWaterMark,e);if(r.endOffset!=null&&(t=Math.min(t,r.endOffset-r.pos)),t<=0){r.destroyed=!0,r.push(null),r.context.unref();return}r.context.pend.go(function(n){if(r.destroyed)return n();var i=Buffer.alloc(t);te.read(r.context.fd,i,0,t,r.pos,function(o,f){o?r.destroy(o):f===0?(r.destroyed=!0,r.push(null),r.context.unref()):(r.pos+=f,r.push(i.slice(0,f))),n()})})}};Ee.prototype.destroy=function(e){this.destroyed||(e=e||new Error("stream destroyed"),this.destroyed=!0,this.emit("error",e),this.context.unref())};xe.inherits(we,$e);function we(e,r){r=r||{},$e.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end==null?1/0:+r.end,this.bytesWritten=0,this.pos=this.start,this.destroyed=!1,this.on("finish",this.destroy.bind(this))}we.prototype._write=function(e,r,t){var n=this;if(!n.destroyed){if(n.pos+e.length>n.endOffset){var i=new Error("maximum file length exceeded");i.code="ETOOBIG",n.destroy(),t(i);return}n.context.pend.go(function(o){if(n.destroyed)return o();te.write(n.context.fd,e,0,e.length,n.pos,function(f,s){f?(n.destroy(),o(),t(f)):(n.bytesWritten+=s,n.pos+=s,n.emit("progress"),o(),t())})})}};we.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.context.unref())};xe.inherits(D,ve);function D(e,r){ve.call(this),r=r||{},this.refCount=0,this.buffer=e,this.maxChunkSize=r.maxChunkSize||Number.MAX_SAFE_INTEGER}D.prototype.read=function(e,r,t,n,i){var o=n+t,f=o-this.buffer.length,s=f>0?f:t;this.buffer.copy(e,r,n,o),setImmediate(function(){i(null,s)})};D.prototype.write=function(e,r,t,n,i){e.copy(this.buffer,n,r,r+t),setImmediate(function(){i(null,t,e)})};D.prototype.createReadStream=function(e){e=e||{};var r=new Lt(e);r.destroyed=!1,r.start=e.start||0,r.endOffset=e.end,r.pos=r.endOffset||this.buffer.length;for(var t=this.buffer.slice(r.start,r.pos),n=0;;){var i=n+this.maxChunkSize;if(i>=t.length){n<t.length&&r.write(t.slice(n,t.length));break}r.write(t.slice(n,i)),n=i}return r.end(),r.destroy=function(){r.destroyed=!0},r};D.prototype.createWriteStream=function(e){var r=this;e=e||{};var t=new $e(e);return t.start=e.start||0,t.endOffset=e.end==null?this.buffer.length:+e.end,t.bytesWritten=0,t.pos=t.start,t.destroyed=!1,t._write=function(n,i,o){if(!t.destroyed){var f=t.pos+n.length;if(f>t.endOffset){var s=new Error("maximum file length exceeded");s.code="ETOOBIG",t.destroyed=!0,o(s);return}n.copy(r.buffer,t.pos,0,n.length),t.bytesWritten+=n.length,t.pos=f,t.emit("progress"),o()}},t.destroy=function(){t.destroyed=!0},t};D.prototype.ref=function(){this.refCount+=1};D.prototype.unref=function(){if(this.refCount-=1,this.refCount<0)throw new Error("invalid unref")};function zt(e,r){return new D(e,r)}function _t(e,r){return new R(e,r)}});var Xe=E(A=>{var je=require("fs"),At=require("zlib"),_r=zr(),Rt=Be(),Ce=require("util"),be=require("events").EventEmitter,Ar=require("stream").Transform,Ke=require("stream").PassThrough,Dt=require("stream").Writable;A.open=Tt;A.fromFd=Rr;A.fromBuffer=Ut;A.fromRandomAccessReader=Ve;A.dosDateTimeToDate=Tr;A.validateFileName=Ur;A.ZipFile=B;A.Entry=ie;A.RandomAccessReader=q;function Tt(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!0),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=ye),je.open(e,"r",function(n,i){if(n)return t(n);Rr(i,r,function(o,f){o&&je.close(i,ye),t(o,f)})})}function Rr(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!1),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=ye),je.fstat(e,function(n,i){if(n)return t(n);var o=_r.createFromFd(e,{autoClose:!0});Ve(o,i.size,r,t)})}function Ut(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose=!1,r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1);var n=_r.createFromBuffer(e,{maxChunkSize:65536});Ve(n,e.length,r,t)}function Ve(e,r,t,n){typeof t=="function"&&(n=t,t=null),t==null&&(t={}),t.autoClose==null&&(t.autoClose=!0),t.lazyEntries==null&&(t.lazyEntries=!1),t.decodeStrings==null&&(t.decodeStrings=!0);var i=!!t.decodeStrings;if(t.validateEntrySizes==null&&(t.validateEntrySizes=!0),t.strictFileNames==null&&(t.strictFileNames=!1),n==null&&(n=ye),typeof r!="number")throw new Error("expected totalSize parameter to be a number");if(r>Number.MAX_SAFE_INTEGER)throw new Error("zip file too large. only file sizes up to 2^52 are supported due to JavaScript's Number type being an IEEE 754 double.");e.ref();var o=22,f=65535,s=Math.min(o+f,r),u=_(s),l=r-u.length;$(e,u,0,s,l,function(a){if(a)return n(a);for(var c=s-o;c>=0;c-=1)if(u.readUInt32LE(c)===101010256){var d=u.slice(c),x=d.readUInt16LE(4);if(x!==0)return n(new Error("multi-disk zip files are not supported: found disk number: "+x));var y=d.readUInt16LE(10),p=d.readUInt32LE(16),h=d.readUInt16LE(20),v=d.length-o;if(h!==v)return n(new Error("invalid comment length. expected: "+v+". found: "+h));var w=i?ge(d,22,d.length,!1):d.slice(22);if(!(y===65535||p===4294967295))return n(null,new B(e,p,r,y,w,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames));var b=_(20),T=l+c-b.length;$(e,b,0,b.length,T,function(Y){if(Y)return n(Y);if(b.readUInt32LE(0)!==117853008)return n(new Error("invalid zip64 end of central directory locator signature"));var k=j(b,8),P=_(56);$(e,P,0,P.length,k,function(ee){return ee?n(ee):P.readUInt32LE(0)!==101075792?n(new Error("invalid zip64 end of central directory record signature")):(y=j(P,32),p=j(P,48),n(null,new B(e,p,r,y,w,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames)))})});return}n(new Error("end of central directory record signature not found"))})}Ce.inherits(B,be);function B(e,r,t,n,i,o,f,s,u,l){var a=this;be.call(a),a.reader=e,a.reader.on("error",function(c){Dr(a,c)}),a.reader.once("close",function(){a.emit("close")}),a.readEntryCursor=r,a.fileSize=t,a.entryCount=n,a.comment=i,a.entriesRead=0,a.autoClose=!!o,a.lazyEntries=!!f,a.decodeStrings=!!s,a.validateEntrySizes=!!u,a.strictFileNames=!!l,a.isOpen=!0,a.emittedError=!1,a.lazyEntries||a._readEntry()}B.prototype.close=function(){this.isOpen&&(this.isOpen=!1,this.reader.unref())};function O(e,r){e.autoClose&&e.close(),Dr(e,r)}function Dr(e,r){e.emittedError||(e.emittedError=!0,e.emit("error",r))}B.prototype.readEntry=function(){if(!this.lazyEntries)throw new Error("readEntry() called without lazyEntries:true");this._readEntry()};B.prototype._readEntry=function(){var e=this;if(e.entryCount===e.entriesRead){setImmediate(function(){e.autoClose&&e.close(),!e.emittedError&&e.emit("end")});return}if(!e.emittedError){var r=_(46);$(e.reader,r,0,r.length,e.readEntryCursor,function(t){if(t)return O(e,t);if(!e.emittedError){var n=new ie,i=r.readUInt32LE(0);if(i!==33639248)return O(e,new Error("invalid central directory file header signature: 0x"+i.toString(16)));if(n.versionMadeBy=r.readUInt16LE(4),n.versionNeededToExtract=r.readUInt16LE(6),n.generalPurposeBitFlag=r.readUInt16LE(8),n.compressionMethod=r.readUInt16LE(10),n.lastModFileTime=r.readUInt16LE(12),n.lastModFileDate=r.readUInt16LE(14),n.crc32=r.readUInt32LE(16),n.compressedSize=r.readUInt32LE(20),n.uncompressedSize=r.readUInt32LE(24),n.fileNameLength=r.readUInt16LE(28),n.extraFieldLength=r.readUInt16LE(30),n.fileCommentLength=r.readUInt16LE(32),n.internalFileAttributes=r.readUInt16LE(36),n.externalFileAttributes=r.readUInt32LE(38),n.relativeOffsetOfLocalHeader=r.readUInt32LE(42),n.generalPurposeBitFlag&64)return O(e,new Error("strong encryption is not supported"));e.readEntryCursor+=46,r=_(n.fileNameLength+n.extraFieldLength+n.fileCommentLength),$(e.reader,r,0,r.length,e.readEntryCursor,function(o){if(o)return O(e,o);if(!e.emittedError){var f=(n.generalPurposeBitFlag&2048)!==0;n.fileName=e.decodeStrings?ge(r,0,n.fileNameLength,f):r.slice(0,n.fileNameLength);var s=n.fileNameLength+n.extraFieldLength,u=r.slice(n.fileNameLength,s);n.extraFields=[];for(var l=0;l<u.length-3;){var a=u.readUInt16LE(l+0),c=u.readUInt16LE(l+2),d=l+4,x=d+c;if(x>u.length)return O(e,new Error("extra field length exceeds extra field buffer size"));var y=_(c);u.copy(y,0,d,x),n.extraFields.push({id:a,data:y}),l=x}if(n.fileComment=e.decodeStrings?ge(r,s,s+n.fileCommentLength,f):r.slice(s,s+n.fileCommentLength),n.comment=n.fileComment,e.readEntryCursor+=r.length,e.entriesRead+=1,n.uncompressedSize===4294967295||n.compressedSize===4294967295||n.relativeOffsetOfLocalHeader===4294967295){for(var p=null,l=0;l<n.extraFields.length;l++){var h=n.extraFields[l];if(h.id===1){p=h.data;break}}if(p==null)return O(e,new Error("expected zip64 extended information extra field"));var v=0;if(n.uncompressedSize===4294967295){if(v+8>p.length)return O(e,new Error("zip64 extended information extra field does not include uncompressed size"));n.uncompressedSize=j(p,v),v+=8}if(n.compressedSize===4294967295){if(v+8>p.length)return O(e,new Error("zip64 extended information extra field does not include compressed size"));n.compressedSize=j(p,v),v+=8}if(n.relativeOffsetOfLocalHeader===4294967295){if(v+8>p.length)return O(e,new Error("zip64 extended information extra field does not include relative header offset"));n.relativeOffsetOfLocalHeader=j(p,v),v+=8}}if(e.decodeStrings)for(var l=0;l<n.extraFields.length;l++){var h=n.extraFields[l];if(h.id===28789){if(h.data.length<6||h.data.readUInt8(0)!==1)continue;var w=h.data.readUInt32LE(1);if(Rt.unsigned(r.slice(0,n.fileNameLength))!==w)continue;n.fileName=ge(h.data,5,h.data.length,!0);break}}if(e.validateEntrySizes&&n.compressionMethod===0){var b=n.uncompressedSize;if(n.isEncrypted()&&(b+=12),n.compressedSize!==b){var T="compressed/uncompressed size mismatch for stored file: "+n.compressedSize+" != "+n.uncompressedSize;return O(e,new Error(T))}}if(e.decodeStrings){e.strictFileNames||(n.fileName=n.fileName.replace(/\\/g,"/"));var Y=Ur(n.fileName,e.validateFileNameOptions);if(Y!=null)return O(e,new Error(Y))}e.emit("entry",n),e.lazyEntries||e._readEntry()}})}})}};B.prototype.openReadStream=function(e,r,t){var n=this,i=0,o=e.compressedSize;if(t==null)t=r,r={};else{if(r.decrypt!=null){if(!e.isEncrypted())throw new Error("options.decrypt can only be specified for encrypted entries");if(r.decrypt!==!1)throw new Error("invalid options.decrypt value: "+r.decrypt);if(e.isCompressed()&&r.decompress!==!1)throw new Error("entry is encrypted and compressed, and options.decompress !== false")}if(r.decompress!=null){if(!e.isCompressed())throw new Error("options.decompress can only be specified for compressed entries");if(!(r.decompress===!1||r.decompress===!0))throw new Error("invalid options.decompress value: "+r.decompress)}if(r.start!=null||r.end!=null){if(e.isCompressed()&&r.decompress!==!1)throw new Error("start/end range not allowed for compressed entry without options.decompress === false");if(e.isEncrypted()&&r.decrypt!==!1)throw new Error("start/end range not allowed for encrypted entry without options.decrypt === false")}if(r.start!=null){if(i=r.start,i<0)throw new Error("options.start < 0");if(i>e.compressedSize)throw new Error("options.start > entry.compressedSize")}if(r.end!=null){if(o=r.end,o<0)throw new Error("options.end < 0");if(o>e.compressedSize)throw new Error("options.end > entry.compressedSize");if(o<i)throw new Error("options.end < options.start")}}if(!n.isOpen)return t(new Error("closed"));if(e.isEncrypted()&&r.decrypt!==!1)return t(new Error("entry is encrypted, and options.decrypt !== false"));n.reader.ref();var f=_(30);$(n.reader,f,0,f.length,e.relativeOffsetOfLocalHeader,function(s){try{if(s)return t(s);var u=f.readUInt32LE(0);if(u!==67324752)return t(new Error("invalid local file header signature: 0x"+u.toString(16)));var l=f.readUInt16LE(26),a=f.readUInt16LE(28),c=e.relativeOffsetOfLocalHeader+f.length+l+a,d;if(e.compressionMethod===0)d=!1;else if(e.compressionMethod===8)d=r.decompress!=null?r.decompress:!0;else return t(new Error("unsupported compression method: "+e.compressionMethod));var x=c,y=x+e.compressedSize;if(e.compressedSize!==0&&y>n.fileSize)return t(new Error("file data overflows file bounds: "+x+" + "+e.compressedSize+" > "+n.fileSize));var p=n.reader.createReadStream({start:x+i,end:x+o}),h=p;if(d){var v=!1,w=At.createInflateRaw();p.on("error",function(b){setImmediate(function(){v||w.emit("error",b)})}),p.pipe(w),n.validateEntrySizes?(h=new oe(e.uncompressedSize),w.on("error",function(b){setImmediate(function(){v||h.emit("error",b)})}),w.pipe(h)):h=w,h.destroy=function(){v=!0,w!==h&&w.unpipe(h),p.unpipe(w),p.destroy()}}t(null,h)}finally{n.reader.unref()}})};function ie(){}ie.prototype.getLastModDate=function(){return Tr(this.lastModFileDate,this.lastModFileTime)};ie.prototype.isEncrypted=function(){return(this.generalPurposeBitFlag&1)!==0};ie.prototype.isCompressed=function(){return this.compressionMethod===8};function Tr(e,r){var t=e&31,n=(e>>5&15)-1,i=(e>>9&127)+1980,o=0,f=(r&31)*2,s=r>>5&63,u=r>>11&31;return new Date(i,n,t,u,s,f,o)}function Ur(e){return e.indexOf("\\")!==-1?"invalid characters in fileName: "+e:/^[a-zA-Z]:/.test(e)||/^\//.test(e)?"absolute path: "+e:e.split("/").indexOf("..")!==-1?"invalid relative path: "+e:null}function $(e,r,t,n,i,o){if(n===0)return setImmediate(function(){o(null,_(0))});e.read(r,t,n,i,function(f,s){if(f)return o(f);if(s<n)return o(new Error("unexpected EOF"));o()})}Ce.inherits(oe,Ar);function oe(e){Ar.call(this),this.actualByteCount=0,this.expectedByteCount=e}oe.prototype._transform=function(e,r,t){if(this.actualByteCount+=e.length,this.actualByteCount>this.expectedByteCount){var n="too many bytes in the stream. expected "+this.expectedByteCount+". got at least "+this.actualByteCount;return t(new Error(n))}t(null,e)};oe.prototype._flush=function(e){if(this.actualByteCount<this.expectedByteCount){var r="not enough bytes in the stream. expected "+this.expectedByteCount+". got only "+this.actualByteCount;return e(new Error(r))}e()};Ce.inherits(q,be);function q(){be.call(this),this.refCount=0}q.prototype.ref=function(){this.refCount+=1};q.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.close(r);function r(t){if(t)return e.emit("error",t);e.emit("close")}};q.prototype.createReadStream=function(e){var r=e.start,t=e.end;if(r===t){var n=new Ke;return setImmediate(function(){n.end()}),n}var i=this._readStreamForRange(r,t),o=!1,f=new Fe(this);i.on("error",function(u){setImmediate(function(){o||f.emit("error",u)})}),f.destroy=function(){i.unpipe(f),f.unref(),i.destroy()};var s=new oe(t-r);return f.on("error",function(u){setImmediate(function(){o||s.emit("error",u)})}),s.destroy=function(){o=!0,f.unpipe(s),f.destroy()},i.pipe(f).pipe(s)};q.prototype._readStreamForRange=function(e,r){throw new Error("not implemented")};q.prototype.read=function(e,r,t,n,i){var o=this.createReadStream({start:n,end:n+t}),f=new Dt,s=0;f._write=function(u,l,a){u.copy(e,r+s,0,u.length),s+=u.length,a()},f.on("finish",i),o.on("error",function(u){i(u)}),o.pipe(f)};q.prototype.close=function(e){setImmediate(e)};Ce.inherits(Fe,Ke);function Fe(e){Ke.call(this),this.context=e,this.context.ref(),this.unreffedYet=!1}Fe.prototype._flush=function(e){this.unref(),e()};Fe.prototype.unref=function(e){this.unreffedYet||(this.unreffedYet=!0,this.context.unref())};var Nt="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";function ge(e,r,t,n){if(n)return e.toString("utf8",r,t);for(var i="",o=r;o<t;o++)i+=Nt[e[o]];return i}function j(e,r){var t=e.readUInt32LE(r),n=e.readUInt32LE(r+4);return n*4294967296+t}var _;typeof Buffer.allocUnsafe=="function"?_=function(e){return Buffer.allocUnsafe(e)}:_=function(e){return new Buffer(e)};function ye(e){if(e)throw e}});var Mr=E((Bn,Nr)=>{var K=1e3,V=K*60,X=V*60,H=X*24,Mt=H*7,Bt=H*365.25;Nr.exports=function(e,r){r=r||{};var t=typeof e;if(t==="string"&&e.length>0)return qt(e);if(t==="number"&&isFinite(e))return r.long?Zt(e):Pt(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function qt(e){if(e=String(e),!(e.length>100)){var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(r){var t=parseFloat(r[1]),n=(r[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return t*Bt;case"weeks":case"week":case"w":return t*Mt;case"days":case"day":case"d":return t*H;case"hours":case"hour":case"hrs":case"hr":case"h":return t*X;case"minutes":case"minute":case"mins":case"min":case"m":return t*V;case"seconds":case"second":case"secs":case"sec":case"s":return t*K;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function Pt(e){var r=Math.abs(e);return r>=H?Math.round(e/H)+"d":r>=X?Math.round(e/X)+"h":r>=V?Math.round(e/V)+"m":r>=K?Math.round(e/K)+"s":e+"ms"}function Zt(e){var r=Math.abs(e);return r>=H?Se(e,r,H,"day"):r>=X?Se(e,r,X,"hour"):r>=V?Se(e,r,V,"minute"):r>=K?Se(e,r,K,"second"):e+" ms"}function Se(e,r,t,n){var i=r>=t*1.5;return Math.round(e/t)+" "+n+(i?"s":"")}});var Je=E((qn,Br)=>{function Wt(e){t.debug=t,t.default=t,t.coerce=u,t.disable=o,t.enable=i,t.enabled=f,t.humanize=Mr(),t.destroy=l,Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={};function r(a){let c=0;for(let d=0;d<a.length;d++)c=(c<<5)-c+a.charCodeAt(d),c|=0;return t.colors[Math.abs(c)%t.colors.length]}t.selectColor=r;function t(a){let c,d=null,x,y;function p(...h){if(!p.enabled)return;let v=p,w=Number(new Date),b=w-(c||w);v.diff=b,v.prev=c,v.curr=w,c=w,h[0]=t.coerce(h[0]),typeof h[0]!="string"&&h.unshift("%O");let T=0;h[0]=h[0].replace(/%([a-zA-Z%])/g,(k,P)=>{if(k==="%%")return"%";T++;let ee=t.formatters[P];if(typeof ee=="function"){let ht=h[T];k=ee.call(v,ht),h.splice(T,1),T--}return k}),t.formatArgs.call(v,h),(v.log||t.log).apply(v,h)}return p.namespace=a,p.useColors=t.useColors(),p.color=t.selectColor(a),p.extend=n,p.destroy=t.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(x!==t.namespaces&&(x=t.namespaces,y=t.enabled(a)),y),set:h=>{d=h}}),typeof t.init=="function"&&t.init(p),p}function n(a,c){let d=t(this.namespace+(typeof c=="undefined"?":":c)+a);return d.log=this.log,d}function i(a){t.save(a),t.namespaces=a,t.names=[],t.skips=[];let c,d=(typeof a=="string"?a:"").split(/[\s,]+/),x=d.length;for(c=0;c<x;c++)d[c]&&(a=d[c].replace(/\*/g,".*?"),a[0]==="-"?t.skips.push(new RegExp("^"+a.slice(1)+"$")):t.names.push(new RegExp("^"+a+"$")))}function o(){let a=[...t.names.map(s),...t.skips.map(s).map(c=>"-"+c)].join(",");return t.enable(""),a}function f(a){if(a[a.length-1]==="*")return!0;let c,d;for(c=0,d=t.skips.length;c<d;c++)if(t.skips[c].test(a))return!1;for(c=0,d=t.names.length;c<d;c++)if(t.names[c].test(a))return!0;return!1}function s(a){return a.toString().substring(2,a.toString().length-2).replace(/\.\*\?$/,"*")}function u(a){return a instanceof Error?a.stack||a.message:a}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}Br.exports=Wt});var qr=E((S,Ie)=>{S.formatArgs=Gt;S.save=Yt;S.load=$t;S.useColors=Ht;S.storage=jt();S.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();S.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Ht(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Gt(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Ie.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let t=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(t++,i==="%c"&&(n=t))}),e.splice(n,0,r)}S.log=console.debug||console.log||(()=>{});function Yt(e){try{e?S.storage.setItem("debug",e):S.storage.removeItem("debug")}catch{}}function $t(){let e;try{e=S.storage.getItem("debug")}catch{}return!e&&typeof process!="undefined"&&"env"in process&&(e=process.env.DEBUG),e}function jt(){try{return localStorage}catch{}}Ie.exports=Je()(S);var{formatters:Kt}=Ie.exports;Kt.j=function(e){try{return JSON.stringify(e)}catch(r){return"[UnexpectedJSONParseError]: "+r.message}}});var Zr=E((Pn,Pr)=>{"use strict";Pr.exports=(e,r=process.argv)=>{let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1||n<i)}});var Gr=E((Zn,Hr)=>{"use strict";var Vt=require("os"),Wr=require("tty"),I=Zr(),{env:g}=process,Le;I("no-color")||I("no-colors")||I("color=false")||I("color=never")?Le=0:(I("color")||I("colors")||I("color=true")||I("color=always"))&&(Le=1);function Xt(){if("FORCE_COLOR"in g)return g.FORCE_COLOR==="true"?1:g.FORCE_COLOR==="false"?0:g.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(g.FORCE_COLOR,10),3)}function Jt(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Qt(e,{streamIsTTY:r,sniffFlags:t=!0}={}){let n=Xt();n!==void 0&&(Le=n);let i=t?Le:n;if(i===0)return 0;if(t){if(I("color=16m")||I("color=full")||I("color=truecolor"))return 3;if(I("color=256"))return 2}if(e&&!r&&i===void 0)return 0;let o=i||0;if(g.TERM==="dumb")return o;if(process.platform==="win32"){let f=Vt.release().split(".");return Number(f[0])>=10&&Number(f[2])>=10586?Number(f[2])>=14931?3:2:1}if("CI"in g)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(f=>f in g)||g.CI_NAME==="codeship"?1:o;if("TEAMCITY_VERSION"in g)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(g.TEAMCITY_VERSION)?1:0;if(g.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in g){let f=Number.parseInt((g.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(g.TERM_PROGRAM){case"iTerm.app":return f>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(g.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(g.TERM)||"COLORTERM"in g?1:o}function Qe(e,r={}){let t=Qt(e,{streamIsTTY:e&&e.isTTY,...r});return Jt(t)}Hr.exports={supportsColor:Qe,stdout:Qe({isTTY:Wr.isatty(1)}),stderr:Qe({isTTY:Wr.isatty(2)})}});var $r=E((C,ze)=>{var kt=require("tty"),Oe=require("util");C.init=sn;C.log=nn;C.formatArgs=rn;C.save=on;C.load=fn;C.useColors=en;C.destroy=Oe.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");C.colors=[6,2,3,4,5,1];try{let e=Gr();e&&(e.stderr||e).level>=2&&(C.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}C.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,r)=>{let t=r.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[r];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[t]=n,e},{});function en(){return"colors"in C.inspectOpts?!!C.inspectOpts.colors:kt.isatty(process.stderr.fd)}function rn(e){let{namespace:r,useColors:t}=this;if(t){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${r} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+ze.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=tn()+r+" "+e[0]}function tn(){return C.inspectOpts.hideDate?"":new Date().toISOString()+" "}function nn(...e){return process.stderr.write(Oe.format(...e)+`
`)}function on(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function fn(){return process.env.DEBUG}function sn(e){e.inspectOpts={};let r=Object.keys(C.inspectOpts);for(let t=0;t<r.length;t++)e.inspectOpts[r[t]]=C.inspectOpts[r[t]]}ze.exports=Je()(C);var{formatters:Yr}=ze.exports;Yr.o=function(e){return this.inspectOpts.colors=this.useColors,Oe.inspect(e,this.inspectOpts).split(`
`).map(r=>r.trim()).join(" ")};Yr.O=function(e){return this.inspectOpts.colors=this.useColors,Oe.inspect(e,this.inspectOpts)}});var jr=E((Wn,ke)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?ke.exports=qr():ke.exports=$r()});var Xr=E((Hn,Vr)=>{Vr.exports=Kr;function Kr(e,r){if(e&&r)return Kr(e)(r);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){t[n]=e[n]}),t;function t(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var o=e.apply(this,n),f=n[n.length-1];return typeof o=="function"&&o!==f&&Object.keys(f).forEach(function(s){o[s]=f[s]}),o}}});var rr=E((Gn,er)=>{var Jr=Xr();er.exports=Jr(_e);er.exports.strict=Jr(Qr);_e.proto=_e(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return _e(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return Qr(this)},configurable:!0})});function _e(e){var r=function(){return r.called?r.value:(r.called=!0,r.value=e.apply(this,arguments))};return r.called=!1,r}function Qr(e){var r=function(){if(r.called)throw new Error(r.onceError);return r.called=!0,r.value=e.apply(this,arguments)},t=e.name||"Function wrapped with `once`";return r.onceError=t+" shouldn't be called more than once",r.called=!1,r}});var rt=E((Yn,et)=>{var an=rr(),un=function(){},cn=function(e){return e.setHeader&&typeof e.abort=="function"},ln=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},kr=function(e,r,t){if(typeof r=="function")return kr(e,null,r);r||(r={}),t=an(t||un);var n=e._writableState,i=e._readableState,o=r.readable||r.readable!==!1&&e.readable,f=r.writable||r.writable!==!1&&e.writable,s=!1,u=function(){e.writable||l()},l=function(){f=!1,o||t.call(e)},a=function(){o=!1,f||t.call(e)},c=function(h){t.call(e,h?new Error("exited with error code: "+h):null)},d=function(h){t.call(e,h)},x=function(){process.nextTick(y)},y=function(){if(!s){if(o&&!(i&&i.ended&&!i.destroyed))return t.call(e,new Error("premature close"));if(f&&!(n&&n.ended&&!n.destroyed))return t.call(e,new Error("premature close"))}},p=function(){e.req.on("finish",l)};return cn(e)?(e.on("complete",l),e.on("abort",x),e.req?p():e.on("request",p)):f&&!n&&(e.on("end",u),e.on("close",u)),ln(e)&&e.on("exit",c),e.on("end",a),e.on("finish",l),r.error!==!1&&e.on("error",d),e.on("close",x),function(){s=!0,e.removeListener("complete",l),e.removeListener("abort",x),e.removeListener("request",p),e.req&&e.req.removeListener("finish",l),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",l),e.removeListener("exit",c),e.removeListener("end",a),e.removeListener("error",d),e.removeListener("close",x)}};et.exports=kr});var it=E(($n,nt)=>{var dn=rr(),hn=rt(),tr=require("fs"),fe=function(){},pn=/^v?\.0/.test(process.version),Ae=function(e){return typeof e=="function"},mn=function(e){return!pn||!tr?!1:(e instanceof(tr.ReadStream||fe)||e instanceof(tr.WriteStream||fe))&&Ae(e.close)},xn=function(e){return e.setHeader&&Ae(e.abort)},vn=function(e,r,t,n){n=dn(n);var i=!1;e.on("close",function(){i=!0}),hn(e,{readable:r,writable:t},function(f){if(f)return n(f);i=!0,n()});var o=!1;return function(f){if(!i&&!o){if(o=!0,mn(e))return e.close(fe);if(xn(e))return e.abort();if(Ae(e.destroy))return e.destroy();n(f||new Error("stream was destroyed"))}}},tt=function(e){e()},En=function(e,r){return e.pipe(r)},wn=function(){var e=Array.prototype.slice.call(arguments),r=Ae(e[e.length-1]||fe)&&e.pop()||fe;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var t,n=e.map(function(i,o){var f=o<e.length-1,s=o>0;return vn(i,f,s,function(u){t||(t=u),u&&n.forEach(tt),!f&&(n.forEach(tt),r(t))})});return e.reduce(En)};nt.exports=wn});var ft=E((jn,ot)=>{"use strict";var{PassThrough:gn}=require("stream");ot.exports=e=>{e={...e};let{array:r}=e,{encoding:t}=e,n=t==="buffer",i=!1;r?i=!(t||n):t=t||"utf8",n&&(t=null);let o=new gn({objectMode:i});t&&o.setEncoding(t);let f=0,s=[];return o.on("data",u=>{s.push(u),i?f=s.length:f+=u.length}),o.getBufferedValue=()=>r?s:n?Buffer.concat(s,f):s.join(""),o.getBufferedLength=()=>f,o}});var st=E((Kn,J)=>{"use strict";var{constants:yn}=require("buffer"),Cn=it(),bn=ft(),Re=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function De(e,r){if(!e)return Promise.reject(new Error("Expected a stream"));r={maxBuffer:1/0,...r};let{maxBuffer:t}=r,n;return await new Promise((i,o)=>{let f=s=>{s&&n.getBufferedLength()<=yn.MAX_LENGTH&&(s.bufferedData=n.getBufferedValue()),o(s)};n=Cn(e,bn(r),s=>{if(s){f(s);return}i()}),n.on("data",()=>{n.getBufferedLength()>t&&f(new Re)})}),n.getBufferedValue()}J.exports=De;J.exports.default=De;J.exports.buffer=(e,r)=>De(e,{...r,encoding:"buffer"});J.exports.array=(e,r)=>De(e,{...r,array:!0});J.exports.MaxBufferError=Re});var ut=E((Vn,at)=>{var z=jr()("extract-zip"),{createWriteStream:Fn,promises:Q}=require("fs"),Sn=st(),G=require("path"),{promisify:ir}=require("util"),In=require("stream"),Ln=Xe(),On=ir(Ln.open),zn=ir(In.pipeline),nr=class{constructor(r,t){this.zipPath=r,this.opts=t}async extract(){return z("opening",this.zipPath,"with opts",this.opts),this.zipfile=await On(this.zipPath,{lazyEntries:!0}),this.canceled=!1,new Promise((r,t)=>{this.zipfile.on("error",n=>{this.canceled=!0,t(n)}),this.zipfile.readEntry(),this.zipfile.on("close",()=>{this.canceled||(z("zip extraction complete"),r())}),this.zipfile.on("entry",async n=>{if(this.canceled){z("skipping entry",n.fileName,{cancelled:this.canceled});return}if(z("zipfile entry",n.fileName),n.fileName.startsWith("__MACOSX/")){this.zipfile.readEntry();return}let i=G.dirname(G.join(this.opts.dir,n.fileName));try{await Q.mkdir(i,{recursive:!0});let o=await Q.realpath(i);if(G.relative(this.opts.dir,o).split(G.sep).includes(".."))throw new Error(`Out of bound path "${o}" found while processing file ${n.fileName}`);await this.extractEntry(n),z("finished processing",n.fileName),this.zipfile.readEntry()}catch(o){this.canceled=!0,this.zipfile.close(),t(o)}})})}async extractEntry(r){if(this.canceled){z("skipping entry extraction",r.fileName,{cancelled:this.canceled});return}this.opts.onEntry&&this.opts.onEntry(r,this.zipfile);let t=G.join(this.opts.dir,r.fileName),n=r.externalFileAttributes>>16&65535,i=61440,o=16384,s=(n&i)===40960,u=(n&i)===o;!u&&r.fileName.endsWith("/")&&(u=!0);let l=r.versionMadeBy>>8;u||(u=l===0&&r.externalFileAttributes===16),z("extracting entry",{filename:r.fileName,isDir:u,isSymlink:s});let a=this.getExtractedMode(n,u)&511,c=u?t:G.dirname(t),d={recursive:!0};if(u&&(d.mode=a),z("mkdir",{dir:c,...d}),await Q.mkdir(c,d),u)return;z("opening read stream",t);let x=await ir(this.zipfile.openReadStream.bind(this.zipfile))(r);if(s){let y=await Sn(x);z("creating symlink",y,t),await Q.symlink(y,t)}else await zn(x,Fn(t,{mode:a}))}getExtractedMode(r,t){let n=r;return n===0&&(t?(this.opts.defaultDirMode&&(n=parseInt(this.opts.defaultDirMode,10)),n||(n=493)):(this.opts.defaultFileMode&&(n=parseInt(this.opts.defaultFileMode,10)),n||(n=420))),n}};at.exports=async function(e,r){if(z("creating target directory",r.dir),!G.isAbsolute(r.dir))throw new Error("Target directory is expected to be absolute");return await Q.mkdir(r.dir,{recursive:!0}),r.dir=await Q.realpath(r.dir),new nr(e,r).extract()}});var An={};wt(An,{extract:()=>_n,yauzl:()=>dt,yazl:()=>lt});module.exports=gt(An);var lt=Te(br()),dt=Te(Xe()),ct=Te(ut()),_n=ct.default;0&&(module.exports={extract,yauzl,yazl});
